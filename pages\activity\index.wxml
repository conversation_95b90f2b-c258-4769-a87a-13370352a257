<view class="container activity-container">
  <diy-navbar isFixed="true" CustomBar='60' bgCustom="#fff">
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-between diygw-col-24" style="padding: 0 30rpx;">
        <view class="back-btn" bind:tap="goBack">
          <text class="diy-icon-back"></text>
        </view>
        <view class="nav-title">活动中心</view>
        <view class="placeholder"></view>
      </view>
    </view>
  </diy-navbar>

  <view class="activity-content">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{error}}" class="error-state">
      <view class="error-text">{{error}}</view>
      <button class="retry-btn" bind:tap="reload">重新加载</button>
    </view>

    <!-- 无活动状态 -->
    <view wx:elif="{{!currentActivity}}" class="empty-state">
      <image src="//xian7.zos.ctyun.cn/pet/static/group12.png" class="empty-image" mode="aspectFit"></image>
      <view class="empty-text">暂无活动</view>
      <view class="empty-desc">敬请期待更多精彩活动</view>
    </view>

    <!-- 活动展示 -->
    <view wx:else class="activity-display">
      <view class="activity-card" bind:tap="viewActivityDetail">
        <view class="activity-cover">
          <image src="{{currentActivity.coverImage}}" class="cover-image" mode="aspectFill"></image>
        </view>
        <view class="activity-info">
          <view class="activity-title">{{currentActivity.title}}</view>
          <view class="activity-time">发布时间：{{currentActivity.formattedPublishedAt}}</view>
          <view class="activity-action">
            <text class="action-text">点击查看详情</text>
            <text class="action-arrow">></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
