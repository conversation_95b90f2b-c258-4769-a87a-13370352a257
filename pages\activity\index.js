import activityApi from '../../api/modules/activity.js';

Page({
  data: {
    //用户全局信息，page-extend.js会自动注入
    userInfo: null,
    currentActivity: null, // 当前活动
    loading: false, // 是否正在加载
    error: null, // 错误信息
  },

  onLoad(options) {
    this.loadCurrentActivity();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadCurrentActivity();
  },

  onPullDownRefresh() {
    this.loadCurrentActivity();
  },

  /**
   * 加载当前活动
   */
  async loadCurrentActivity() {
    if (this.data.loading) return;

    this.setData({ 
      loading: true,
      error: null,
    });

    try {
      const result = await activityApi.getCurrent({ target: '用户端' });
      
      this.setData({
        currentActivity: result ? {
          ...result,
          formattedPublishedAt: this.formatTime(result.publishedAt),
        } : null,
      });
    } catch (error) {
      console.error('加载活动失败:', error);
      this.setData({
        error: '加载失败，请重试',
      });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 查看活动详情
   */
  viewActivityDetail() {
    const { currentActivity } = this.data;
    if (!currentActivity || !currentActivity.id) {
      wx.showToast({
        title: '活动信息错误',
        icon: 'none',
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/activity/detail?id=${currentActivity.id}`,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
        });
      }
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      fail: () => {
        // 如果没有上一页，则跳转到首页
        wx.redirectTo({
          url: '/pages/index/index'
        });
      }
    });
  },

  /**
   * 重新加载
   */
  reload() {
    this.loadCurrentActivity();
  },

  /**
   * 格式化时间
   */
  formatTime(dateString) {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return dateString;
    }
  },
});
