<view class="container activity-detail-container">
  <diy-navbar isFixed="true" CustomBar='60' bgCustom="#fff">
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-between diygw-col-24" style="padding: 0 30rpx;">
        <view class="back-btn" bind:tap="goBack">
          <text class="diy-icon-back"></text>
        </view>
        <view class="nav-title">活动详情</view>
        <view class="placeholder"></view>
      </view>
    </view>
  </diy-navbar>

  <view class="detail-content">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{error}}" class="error-state">
      <view class="error-text">{{error}}</view>
      <button class="retry-btn" bind:tap="reload">重新加载</button>
    </view>

    <!-- 活动详情展示 -->
    <view wx:elif="{{activityDetail}}" class="activity-detail">
      <!-- 活动封面 -->
      <view class="activity-header">
        <image src="{{activityDetail.coverImage}}" class="header-image" mode="aspectFill"></image>
        <view class="header-overlay">
          <view class="activity-title">{{activityDetail.title}}</view>
          <view class="activity-time">发布时间：{{activityDetail.formattedPublishedAt}}</view>
        </view>
      </view>

      <!-- 活动内容 -->
      <view class="activity-content-wrapper">
        <!-- 富文本内容 -->
        <view wx:if="{{activityDetail.contentType === 'content' && activityDetail.content}}" class="rich-text-content">
          <rich-text nodes="{{activityDetail.content}}"></rich-text>
        </view>

        <!-- WebView内容 -->
        <view wx:elif="{{activityDetail.contentType === 'url' && activityDetail.url}}" class="webview-content">
          <web-view src="{{activityDetail.url}}"></web-view>
        </view>

        <!-- 无内容状态 -->
        <view wx:else class="no-content">
          <view class="no-content-text">活动内容暂未配置</view>
        </view>
      </view>
    </view>
  </view>
</view>
