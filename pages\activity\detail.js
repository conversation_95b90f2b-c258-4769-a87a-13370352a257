import activityApi from '../../api/modules/activity.js';

Page({
  data: {
    //用户全局信息，page-extend.js会自动注入
    userInfo: null,
    activityDetail: null, // 活动详情
    loading: false, // 是否正在加载
    error: null, // 错误信息
    activityId: null, // 活动ID
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      this.setData({
        error: '活动ID不能为空',
      });
      return;
    }

    this.setData({ activityId: id });
    this.loadActivityDetail();
  },

  /**
   * 加载活动详情
   */
  async loadActivityDetail() {
    if (this.data.loading) return;

    this.setData({ 
      loading: true,
      error: null,
    });

    try {
      const result = await activityApi.getDetail(this.data.activityId);
      
      if (result) {
        this.setData({
          activityDetail: {
            ...result,
            formattedPublishedAt: this.formatTime(result.publishedAt),
          },
        });
      } else {
        this.setData({
          error: '活动不存在或已结束',
        });
      }
    } catch (error) {
      console.error('加载活动详情失败:', error);
      this.setData({
        error: '加载失败，请重试',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      fail: () => {
        // 如果没有上一页，则跳转到活动首页
        wx.redirectTo({
          url: '/pages/activity/index'
        });
      }
    });
  },

  /**
   * 重新加载
   */
  reload() {
    this.loadActivityDetail();
  },

  /**
   * 格式化时间
   */
  formatTime(dateString) {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return dateString;
    }
  },
});
